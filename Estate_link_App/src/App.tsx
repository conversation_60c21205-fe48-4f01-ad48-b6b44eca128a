import React from 'react';
import { Provider } from 'react-redux';
import { store } from './store';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { EmailCheckScreen } from './Features/EmailCheckScreen/EmailCheckScreen';
import { Login } from './Features/LoginScreen/Login';
import { SetPasswordScreen } from './Features/SetPasswordScreen/SetPasswordScreen';
import { DashboardScreen } from './Features/DashboardScreen/DashboardScreen';
import { ForgotPassword } from './Features/ForgetPasswordScreen/ForgotPassword';
import { VerifyCode } from './Features/VerifyCodeScreen/VerifyCode';
import { WelcomeBack } from './Features/WelcomeBackScreen/WelcomeBack';

const Stack = createStackNavigator();

export default function App() {
  return (
    <Provider store={store}>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="EmailCheck"
          screenOptions={{
            headerShown: false,
          }}>
          <Stack.Screen name="EmailCheck" component={EmailCheckScreen} />
          <Stack.Screen name="Login" component={Login} />
          <Stack.Screen name="SetPassword" component={SetPasswordScreen} />
          <Stack.Screen name="Dashboard" component={DashboardScreen} />
          <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
          <Stack.Screen name="VerifyCode" component={VerifyCode} />
          <Stack.Screen name="WelcomeBack" component={WelcomeBack} />
        </Stack.Navigator>
      </NavigationContainer>
    </Provider>
  );
} 