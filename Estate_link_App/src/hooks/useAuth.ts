import { useState, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { 
  checkUserStatus as checkUserStatusAction, 
  setPasswordUser as setPasswordUserAction,
  loginUser as loginUserAction,
  logoutUser as logoutUserAction,
  clearError 
} from '../store/slices/authSlice';
import { checkUserStatus, setPasswordUser, loginUser, logoutUser } from '../api/authApi/authApi';
import type { CheckStatusResponse, SetPasswordResponse, LoginResponse } from '../api/authApi/authApi';

export const useAuth = () => {
  const dispatch = useAppDispatch();
  const { isLoading, error, user, isAuthenticated } = useAppSelector((state) => state.auth);
  
  const [localError, setLocalError] = useState<string | null>(null);
  const [localSuccess, setLocalSuccess] = useState<string | null>(null);

  // Clear local messages
  const clearMessages = useCallback(() => {
    setLocalError(null);
    setLocalSuccess(null);
    dispatch(clearError());
  }, [dispatch]);

  // Check user status - determines if user exists and if it's their first login
  const checkUserStatusHook = useCallback(async (authenticator: string): Promise<CheckStatusResponse | null> => {
    clearMessages();
    
    try {
      const result = await dispatch(checkUserStatusAction(authenticator)).unwrap();
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to check user status';
      setLocalError(errorMessage);
      return null;
    }
  }, [dispatch, clearMessages]);

  // Set password for first-time users
  const setPasswordUserHook = useCallback(async (
    userId: string,
    newPassword: string,
    oldPassword: string,
    confirmPassword: string
  ): Promise<SetPasswordResponse | null> => {
    clearMessages();
    
    try {
      const result = await dispatch(setPasswordUserAction({
        userId,
        newPassword,
        oldPassword,
        confirmPassword
      })).unwrap();
      
      setLocalSuccess('Password set successfully!');
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to set password';
      setLocalError(errorMessage);
      return null;
    }
  }, [dispatch, clearMessages]);

  // Login user with credentials
  const loginUserHook = useCallback(async (
    authenticator: string,
    password: string,
    login_type: string = 'org'
  ): Promise<LoginResponse | null> => {
    clearMessages();
    
    try {
      const result = await dispatch(loginUserAction({
        authenticator,
        password,
        login_type
      })).unwrap();
      
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Login failed';
      setLocalError(errorMessage);
      return null;
    }
  }, [dispatch, clearMessages]);

  // Logout user
  const logoutUserHook = useCallback(async (refreshToken: string): Promise<boolean> => {
    clearMessages();
    
    try {
      await dispatch(logoutUserAction(refreshToken)).unwrap();
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Logout failed';
      setLocalError(errorMessage);
      return false;
    }
  }, [dispatch, clearMessages]);

  // Direct API calls (for cases where you don't want to use Redux)
  const checkUserStatusDirect = useCallback(async (authenticator: string): Promise<CheckStatusResponse | null> => {
    clearMessages();
    
    try {
      const result = await checkUserStatus(authenticator);
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to check user status';
      setLocalError(errorMessage);
      return null;
    }
  }, [clearMessages]);

  const setPasswordUserDirect = useCallback(async (
    userId: string,
    newPassword: string,
    oldPassword: string,
    confirmPassword: string
  ): Promise<SetPasswordResponse | null> => {
    clearMessages();
    
    try {
      const result = await setPasswordUser(userId, newPassword, oldPassword, confirmPassword);
      setLocalSuccess('Password set successfully!');
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to set password';
      setLocalError(errorMessage);
      return null;
    }
  }, [clearMessages]);

  const loginUserDirect = useCallback(async (
    authenticator: string,
    password: string,
    login_type: string = 'org'
  ): Promise<LoginResponse | null> => {
    clearMessages();
    
    try {
      const result = await loginUser(authenticator, password, login_type);
      return result;
    } catch (error: any) {
      const errorMessage = error.message || 'Login failed';
      setLocalError(errorMessage);
      return null;
    }
  }, [clearMessages]);

  const logoutUserDirect = useCallback(async (refreshToken: string): Promise<boolean> => {
    clearMessages();
    
    try {
      await logoutUser(refreshToken);
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Logout failed';
      setLocalError(errorMessage);
      return false;
    }
  }, [clearMessages]);

  return {
    // State
    isLoading,
    error: error || localError,
    success: localSuccess,
    user,
    isAuthenticated,
    
    // Actions with Redux
    checkUserStatus: checkUserStatusHook,
    setPasswordUser: setPasswordUserHook,
    loginUser: loginUserHook,
    logoutUser: logoutUserHook,
    
    // Direct API calls
    checkUserStatusDirect,
    setPasswordUserDirect,
    loginUserDirect,
    logoutUserDirect,
    
    // Utilities
    clearMessages,
    clearError: clearMessages,
  };
}; 