import { useState, useCallback } from 'react';
import * as yup from 'yup';
import { ValidationError } from 'yup';

interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export const useFormValidation = <T extends Record<string, any>>(
  schema: yup.ObjectSchema<T>
) => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = useCallback(
    async (field: keyof T, value: any): Promise<string | null> => {
      try {
        await schema.validateAt(field as string, { [field]: value });
        return null;
      } catch (error) {
        if (error instanceof ValidationError) {
          return error.message;
        }
        return 'Validation error';
      }
    },
    [schema]
  );

  const validateForm = useCallback(
    async (data: T): Promise<ValidationResult> => {
      try {
        await schema.validate(data, { abortEarly: false });
        setErrors({});
        return { isValid: true, errors: {} };
      } catch (error) {
        if (error instanceof ValidationError) {
          const newErrors: Record<string, string> = {};
          error.inner.forEach((err) => {
            if (err.path) {
              newErrors[err.path] = err.message;
            }
          });
          setErrors(newErrors);
          return { isValid: false, errors: newErrors };
        }
        return { isValid: false, errors: {} };
      }
    },
    [schema]
  );

  const setFieldTouched = useCallback((field: keyof T, isTouched: boolean = true) => {
    setTouched((prev) => ({ ...prev, [field]: isTouched }));
  }, []);

  const setFieldError = useCallback((field: keyof T, error: string | null) => {
    setErrors((prev) => {
      if (error === null) {
        const newErrors = { ...prev };
        delete newErrors[field as string];
        return newErrors;
      }
      return { ...prev, [field]: error };
    });
  }, []);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const clearTouched = useCallback(() => {
    setTouched({});
  }, []);

  const getFieldError = useCallback(
    (field: keyof T): string | undefined => {
      return errors[field as string];
    },
    [errors]
  );

  const isFieldTouched = useCallback(
    (field: keyof T): boolean => {
      return touched[field as string] || false;
    },
    [touched]
  );

  const hasErrors = Object.keys(errors).length > 0;

  return {
    errors,
    touched,
    hasErrors,
    validateField,
    validateForm,
    setFieldTouched,
    setFieldError,
    clearErrors,
    clearTouched,
    getFieldError,
    isFieldTouched,
  };
}; 