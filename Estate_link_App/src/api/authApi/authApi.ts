import { API_CONFIG, enhancedFetch, getNetworkErrorMessage, isNetworkError, checkNetworkConnectivity, tryMultipleUrls } from '../../utils/networkUtils';

const API_URL = `${API_CONFIG.BASE_URL}/user`;

// Types for API responses
export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  member: any;
  permission_ids: string[];
}

export interface CheckStatusResponse {
  is_first_login: boolean;
  user_id: string;
  message?: string;
}

export interface SetPasswordResponse {
  message: string;
  success: boolean;
}

export interface ApiError {
  error: string;
  [key: string]: any;
}

// Helper function to retry failed requests
const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxRetries = API_CONFIG.RETRY_ATTEMPTS,
  delay = API_CONFIG.RETRY_DELAY
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error as Error;
      console.log(`Request attempt ${attempt} failed:`, lastError.message);

      if (attempt === maxRetries) {
        break;
      }

      // Check network connectivity before retrying
      console.log('Checking network connectivity before retry...');
      const isConnected = await checkNetworkConnectivity();
      if (!isConnected) {
        console.log('Network connectivity check failed, aborting retries');
        throw new Error('No internet connection. Please check your network and try again.');
      }

      // Wait before retrying with exponential backoff
      const retryDelay = delay * Math.pow(2, attempt - 1);
      console.log(`Waiting ${retryDelay}ms before retry ${attempt + 1}...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  throw lastError!;
};

// Check user status - determines if user exists and if it's their first login
export const checkUserStatus = async (authenticator: string): Promise<CheckStatusResponse> => {
  console.log('Making API call to check_status endpoint');
  console.log('Request payload:', { authenticator });
  
  return retryRequest(async () => {
    try {
      console.log('Starting fetch request with fallback URLs...');
      const response = await tryMultipleUrls(
        '/user/check_status/',
        {
          method: 'POST',
          body: JSON.stringify({ authenticator }),
        },
        API_CONFIG.TIMEOUT
      );
      
      console.log('Response received!');
      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.log('Error response:', errorData);

        // Handle 404 as a valid "user not found" response
        if (response.status === 404) {
          throw new Error(errorData.error || errorData || 'User not found');
        }

        throw new Error(errorData.error || errorData || 'Status check failed');
      }

      const data = await response.json();
      console.log('Success response:', data);
      return data;
    } catch (error) {
      console.error('checkUserStatus error:', error);
      throw error;
    }
  });
};

// Set password for first-time users
export const setPasswordUser = async (
  userId: string,
  newPassword: string,
  oldPassword: string,
  confirmPassword: string
): Promise<SetPasswordResponse> => {
  console.log('Making API call to:', `${API_URL}/set_password/`);
  console.log('Request payload:', { user_id: userId, new_password: newPassword, old_password: oldPassword, confirm_password: confirmPassword });
  
  return retryRequest(async () => {
    try {
      const response = await enhancedFetch(
        `${API_URL}/set_password/`,
        {
          method: 'POST',
          body: JSON.stringify({
            user_id: userId,
            new_password: newPassword,
            old_password: oldPassword,
            confirm_password: confirmPassword
          }),
        },
        API_CONFIG.TIMEOUT
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.log('Error response:', errorData);
        throw new Error(errorData.error || errorData || 'Set password failed');
      }

      const data = await response.json();
      console.log('Success response:', data);
      return data;
    } catch (error) {
      console.error('setPasswordUser error:', error);
      throw error;
    }
  });
};

// Login user with credentials
export const loginUser = async (
  authenticator: string,
  password: string,
  login_type: string = 'org'
): Promise<LoginResponse> => {
  console.log('Making API call to:', `${API_URL}/login/`);
  console.log('Request payload:', { authenticator, password, login_type });
  
  return retryRequest(async () => {
    try {
      const response = await enhancedFetch(
        `${API_URL}/login/`,
        {
          method: 'POST',
          body: JSON.stringify({
            authenticator,
            password,
            login_type
          }),
        },
        API_CONFIG.TIMEOUT
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.log('Error response:', errorData);
        throw new Error(errorData.error || errorData || 'Login failed');
      }

      const data = await response.json();
      console.log('Success response:', data);
      return data;
    } catch (error) {
      console.error('loginUser error:', error);
      throw error;
    }
  });
};

// Logout user
export const logoutUser = async (refreshToken: string): Promise<void> => {
  console.log('Making API call to:', `${API_URL}/logout/`);
  
  return retryRequest(async () => {
    try {
      const response = await enhancedFetch(
        `${API_URL}/logout/`,
        {
          method: 'POST',
          body: JSON.stringify({ refresh_token: refreshToken }),
        },
        API_CONFIG.TIMEOUT
      );
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.log('Error response:', errorData);
        throw new Error(errorData.error || errorData || 'Logout failed');
      }

      console.log('Logout successful');
    } catch (error) {
      console.error('logoutUser error:', error);
      throw error;
    }
  });
}; 