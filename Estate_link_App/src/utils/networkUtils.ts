// Network utility functions
export const checkNetworkConnectivity = async (): Promise<boolean> => {
  try {
    // First try to reach our backend server with a simple endpoint
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout for connectivity check

    await fetch(`${API_CONFIG.BASE_URL}/`, {
      method: 'HEAD',
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return true; // If we can reach the server, even with an error response, network is working
  } catch (error) {
    console.log('Backend connectivity check failed, trying Google:', error);

    // Fallback to Google connectivity check
    try {
      const response = await fetch('https://www.google.com', {
        method: 'HEAD'
      });
      return response.ok;
    } catch (fallbackError) {
      console.log('Network connectivity check failed:', fallbackError);
      return false;
    }
  }
};

export const getNetworkErrorMessage = (error: any): string => {
  if (error.name === 'AbortError' || error.message === 'Aborted') {
    return 'Request timed out. Please check your internet connection and try again.';
  }
  
  if (error.message?.includes('Network request failed')) {
    return 'Network error. Please check your internet connection and try again.';
  }
  
  if (error.message?.includes('fetch')) {
    return 'Unable to connect to server. Please try again later.';
  }
  
  if (error.message?.includes('timeout')) {
    return 'Request timed out. Please try again.';
  }
  
  return error.message || 'An unexpected error occurred. Please try again.';
};

export const isNetworkError = (error: any): boolean => {
  return (
    error.name === 'AbortError' ||
    error.message?.includes('Network request failed') ||
    error.message?.includes('fetch') ||
    error.message?.includes('timeout') ||
    error.message?.includes('network') ||
    error.message?.includes('connection')
  );
};

// Get the appropriate base URL for different environments
const getBaseUrl = () => {
  // For Android emulator, use ******** to access host machine's localhost
  // For iOS simulator, use localhost or 127.0.0.1
  // For physical devices, use your computer's IP address on the local network

  // You can uncomment the appropriate line based on your setup:

  // For Android Emulator (requires Django server to run on 0.0.0.0:8000):
  return 'http://********:8000';

  // For iOS Simulator:
  // return 'http://localhost:8000';

  // For Physical Device and Android Emulator (using your computer's IP):
  return 'http://*************:8000';

  // For development with your current setup (if Django runs on 127.0.0.1:8000):
  // return 'http://127.0.0.1:8000';

  // Alternative for testing - use localhost if Django server is accessible:
  // return 'http://localhost:8000';
};

// API configuration
export const API_CONFIG = {
  BASE_URL: getBaseUrl(),
  TIMEOUT: 15000, // 15 seconds - reduced from 30 seconds for faster feedback
  RETRY_ATTEMPTS: 2, // Reduced from 3 to 2
  RETRY_DELAY: 1000, // 1 second - reduced from 2 seconds
};

// Enhanced fetch with better error handling and fallback URLs
export const enhancedFetch = async (
  url: string, 
  options: RequestInit = {}, 
  timeout = API_CONFIG.TIMEOUT
): Promise<Response> => {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
};

// Try multiple URLs to find a working connection
export const tryMultipleUrls = async (
  endpoint: string,
  options: RequestInit = {},
  timeout = API_CONFIG.TIMEOUT
): Promise<Response> => {
  const urls = [
    'http://********:8000',  // Android emulator
    'http://*************:8000',  // Your computer's IP
    'http://localhost:8000',  // Localhost
    'http://127.0.0.1:8000',  // 127.0.0.1
  ];

  let lastError: any = null;

  for (const baseUrl of urls) {
    try {
      console.log(`Trying URL: ${baseUrl}${endpoint}`);
      const response = await enhancedFetch(`${baseUrl}${endpoint}`, options, timeout);
      console.log(`Success with URL: ${baseUrl}${endpoint}`);
      return response;
    } catch (error) {
      console.log(`Failed with URL: ${baseUrl}${endpoint}`, error);
      lastError = error;
      continue;
    }
  }

  throw lastError || new Error('All URLs failed');
}; 