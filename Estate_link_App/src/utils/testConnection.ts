import { API_CONFIG } from './networkUtils';

export const testBackendConnection = async () => {
  console.log('=== Testing Backend Connection ===');
  console.log('API Base URL:', API_CONFIG.BASE_URL);
  
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Connection successful!');
      console.log('Response:', data);
      return { success: true, data };
    } else {
      console.log('❌ Connection failed with status:', response.status);
      return { success: false, status: response.status };
    }
  } catch (error) {
    console.log('❌ Connection error:', error);
    return { success: false, error };
  }
}; 