import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StatusBar,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from 'hooks/useAuth';

type RootStackParamList = {
  Dashboard: undefined;
  EmailCheck: undefined;
};

type DashboardScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Dashboard'>;

export function DashboardScreen() {
  const navigation = useNavigation<DashboardScreenNavigationProp>();
  const { logoutUser, user, isAuthenticated } = useAuth();

  const handleLogout = async () => {
    try {
      // For now, just navigate back to email check
      // In a real app, you would call logoutUser with the refresh token
      navigation.navigate('EmailCheck');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  if (!isAuthenticated) {
    navigation.navigate('EmailCheck');
    return null;
  }

  return (
    <View className="flex-1 bg-white">
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      {/* Header */}
      <View className="flex-row items-center justify-between px-6 py-4 pt-12">
        <Text
          className="font-oxanium-bold text-text-primary"
          style={{ fontSize: 24, fontWeight: '600' }}>
          Dashboard
        </Text>
        <TouchableOpacity
          onPress={handleLogout}
          className="bg-red-500 px-4 py-2 rounded-lg">
          <Text className="text-white font-oxanium-bold">Logout</Text>
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      <View className="flex-1 px-6 pt-10">
        {/* Logo Section */}
        <View className="items-center" style={{ marginBottom: 60 }}>
          <Image
            source={require('../../../assets/Logo.png')}
            style={{ width: 200, height: 64, marginBottom: 50 }}
            resizeMode="contain"
          />

          <Text
            className="text-center font-oxanium-bold text-text-primary"
            style={{
              fontSize: 30,
              fontWeight: '500',
              marginBottom: 8,
              lineHeight: 30,
              letterSpacing: 0.3,
            }}>
            Welcome to Estate Link
          </Text>

          <Text
            className="text-center font-oxanium text-text-secondary"
            style={{ fontSize: 16, fontWeight: '500' }}>
            You have successfully logged in!
          </Text>
        </View>

        {/* User Info */}
        <View className="bg-gray-50 p-6 rounded-lg mb-6">
          <Text
            className="font-oxanium-bold text-text-primary mb-4"
            style={{ fontSize: 18, fontWeight: '600' }}>
            User Information
          </Text>
          
          {user && (
            <View>
              {user.username && (
                <Text className="font-oxanium text-text-secondary mb-2">
                  Username: {user.username}
                </Text>
              )}
              {user.email && (
                <Text className="font-oxanium text-text-secondary mb-2">
                  Email: {user.email}
                </Text>
              )}
              {user.phone && (
                <Text className="font-oxanium text-text-secondary mb-2">
                  Phone: {user.phone}
                </Text>
              )}
            </View>
          )}
        </View>

        {/* Features */}
        <View className="space-y-4">
          <Text
            className="font-oxanium-bold text-text-primary"
            style={{ fontSize: 18, fontWeight: '600', marginBottom: 16 }}>
            Available Features
          </Text>
          
          <View className="bg-primary p-4 rounded-lg">
            <Text className="text-white font-oxanium-bold mb-2">Announcements</Text>
            <Text className="text-white font-oxanium opacity-90">
              View and manage community announcements
            </Text>
          </View>
          
          <View className="bg-primary p-4 rounded-lg">
            <Text className="text-white font-oxanium-bold mb-2">Notices</Text>
            <Text className="text-white font-oxanium opacity-90">
              Access important notices and updates
            </Text>
          </View>
          
          <View className="bg-primary p-4 rounded-lg">
            <Text className="text-white font-oxanium-bold mb-2">Groups</Text>
            <Text className="text-white font-oxanium opacity-90">
              Manage community groups and members
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
} 