import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StatusBar,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { ErrorMessage } from 'components';
import { useAuth } from 'hooks/useAuth';
import { useFormValidation } from 'hooks/useFormValidation';
import * as yup from 'yup';

type RootStackParamList = {
  EmailCheck: undefined;
  SetPassword: { userId: string; authenticator: string };
  Login: { authenticator: string };
  WelcomeBack: { authenticator: string };
};

type EmailCheckScreenNavigationProp = StackNavigationProp<RootStackParamList, 'EmailCheck'>;

export function EmailCheckScreen() {
  const navigation = useNavigation<EmailCheckScreenNavigationProp>();
  const { checkUserStatus, isLoading, error, clearMessages } = useAuth();

  const [formData, setFormData] = useState({
    authenticator: '',
  });

  const {
    errors,
    validateForm,
    setFieldTouched,
    getFieldError,
    isFieldTouched,
    clearErrors,
  } = useFormValidation(
    yup.object({
      authenticator: yup.string().required('Email/Username/Phone is required').min(3, 'Must be at least 3 characters'),
    })
  );

  const handleInputChange = async (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (error) {
      clearMessages();
    }

    // Validate field on change
    if (isFieldTouched(field as keyof typeof formData)) {
      const fieldError = await validateForm({ ...formData, [field]: value });
      if (fieldError.isValid) {
        // Clear field error if validation passes
      }
    }
  };

  const handleBlur = (field: string) => {
    setFieldTouched(field as keyof typeof formData, true);
  };

  const handleCheckStatus = async () => {
    // Clear previous errors
    clearErrors();
    clearMessages();

    // Validate form
    const validation = await validateForm(formData);
    if (!validation.isValid) {
      return;
    }

    try {
      console.log('Checking user status for:', formData.authenticator);
      
      const result = await checkUserStatus(formData.authenticator.trim());
      
      if (result) {
        if (result.is_first_login) {
          // Navigate to set password for first-time users
          navigation.navigate('SetPassword', {
            userId: result.user_id,
            authenticator: formData.authenticator.trim()
          });
        } else {
          // Navigate to login for existing users
          navigation.navigate('Login', {
            authenticator: formData.authenticator.trim()
          });
        }
      }
    } catch (error: any) {
      console.error('Email check error:', error);
      Alert.alert('Error', error.message || 'Failed to check user status');
    }
  };

  const authenticatorError = getFieldError('authenticator');
  const showAuthenticatorError = isFieldTouched('authenticator') && authenticatorError;
  const isFormValid = formData.authenticator.trim() && !authenticatorError;

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Status Bar Area */}
        <View className="h-11 flex-row items-center justify-between px-6 pb-2 pt-32"></View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-10">
          {/* Logo Section */}
          <View className="items-center" style={{ marginBottom: 80 }}>
            <Image
              source={require('../../../assets/Logo.png')}
              style={{ width: 200, height: 64, marginBottom: 50 }}
              resizeMode="contain"
            />

            <Text
              className="text-center font-oxanium-bold text-text-primary"
              style={{
                fontSize: 30,
                fontWeight: '500',
                marginBottom: 8,
                lineHeight: 30,
                letterSpacing: 0.3,
              }}>
              Welcome to Estate Link
            </Text>

            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 16, fontWeight: '500' }}>
              Enter your email, username, or phone number to continue
            </Text>
          </View>

          {/* Form Section */}
          <View>
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              Email / Username / Phone Number
            </Text>

            <TextInput
              className={`rounded-lg border bg-background-input text-text-primary ${
                showAuthenticatorError ? 'border-red-500' : 'border-border'
              }`}
              style={{
                height: 48,
                borderWidth: 1,
                paddingHorizontal: 16,
                fontSize: 14,
                marginBottom: showAuthenticatorError ? 8 : 24,
                fontFamily: 'Oxanium-Medium',
              }}
              placeholder="Enter your email, username, or phone number"
              placeholderTextColor="#9CA3AF"
              value={formData.authenticator}
              onChangeText={(value) => handleInputChange('authenticator', value)}
              onBlur={() => handleBlur('authenticator')}
              returnKeyType="next"
              onSubmitEditing={handleCheckStatus}
              blurOnSubmit={true}
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />

            {/* Error Message */}
            {showAuthenticatorError && (
              <Text
                className="text-red-500 font-oxanium"
                style={{ fontSize: 12, marginBottom: 16 }}>
                {authenticatorError}
              </Text>
            )}

            {/* Global Error Message */}
            {error && (
              <View style={{ marginBottom: 16 }}>
                <ErrorMessage message={error} visible={true} />
              </View>
            )}

            {/* Continue Button */}
            <TouchableOpacity
              className={`rounded-lg ${
                isFormValid && !isLoading
                  ? 'bg-primary'
                  : 'bg-gray-300'
              }`}
              style={{
                height: 48,
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 16,
              }}
              onPress={handleCheckStatus}
              disabled={!isFormValid || isLoading}>
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text
                  className="font-oxanium-bold text-white"
                  style={{ fontSize: 16, fontWeight: '600' }}>
                  Continue
                </Text>
              )}
            </TouchableOpacity>

            {/* Help Text */}
            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 14, fontWeight: '400' }}>
              We'll check if you have an account and guide you through the next steps
            </Text>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
} 