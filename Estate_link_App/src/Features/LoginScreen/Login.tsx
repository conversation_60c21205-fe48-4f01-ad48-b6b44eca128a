import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StatusBar,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { RouteProp } from '@react-navigation/native';
import { ErrorMessage } from 'components';
import { useAuth } from 'hooks/useAuth';
import { useFormValidation } from 'hooks/useFormValidation';
import * as yup from 'yup';

type RootStackParamList = {
  Login: { authenticator: string };
  WelcomeBack: { authenticator: string };
  SetPassword: { userId: string; authenticator: string };
  ForgotPassword: undefined;
  Dashboard: undefined;
};

type LoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Login'>;
type LoginScreenRouteProp = RouteProp<RootStackParamList, 'Login'>;

export function Login() {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const route = useRoute<LoginScreenRouteProp>();
  const { loginUser, isLoading, error, clearMessages, isAuthenticated } = useAuth();

  const [formData, setFormData] = useState({
    password: '',
  });

  const {
    errors,
    validateForm,
    setFieldTouched,
    getFieldError,
    isFieldTouched,
    clearErrors,
  } = useFormValidation(
    yup.object({
      password: yup.string().required('Password is required').min(6, 'Password must be at least 6 characters'),
    })
  );

  // Get authenticator from route params
  const authenticator = route.params?.authenticator || '';

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigation.navigate('Dashboard');
    }
  }, [isAuthenticated, navigation]);

  const handleInputChange = async (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (error) {
      clearMessages();
    }

    // Validate field on change
    if (isFieldTouched(field as keyof typeof formData)) {
      const fieldError = await validateForm({ ...formData, [field]: value });
      if (fieldError.isValid) {
        // Clear field error if validation passes
      }
    }
  };

  const handleBlur = (field: string) => {
    setFieldTouched(field as keyof typeof formData, true);
  };

  const handleLogin = async () => {
    // Clear previous errors
    clearErrors();
    clearMessages();

    // Validate form
    const validation = await validateForm(formData);
    if (!validation.isValid) {
      return;
    }

    try {
      console.log('Logging in with:', { authenticator, password: formData.password });
      
      const result = await loginUser(authenticator, formData.password);
      
      if (result) {
        // Login successful, navigate to dashboard
        navigation.navigate('Dashboard');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      Alert.alert('Error', error.message || 'Login failed');
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const passwordError = getFieldError('password');
  const showPasswordError = isFieldTouched('password') && passwordError;
  const isFormValid = formData.password.trim() && !passwordError;

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Status Bar Area */}
        <View className="h-11 flex-row items-center justify-between px-6 pb-2 pt-32"></View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-10">
          {/* Logo Section */}
          <View className="items-center" style={{ marginBottom: 80 }}>
            <Image
              source={require('../../../assets/Logo.png')}
              style={{ width: 200, height: 64, marginBottom: 50 }}
              resizeMode="contain"
            />

            <Text
              className="text-center font-oxanium-bold text-text-primary"
              style={{
                fontSize: 30,
                fontWeight: '500',
                marginBottom: 8,
                lineHeight: 30,
                letterSpacing: 0.3,
              }}>
              Welcome Back
            </Text>

            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 16, fontWeight: '500' }}>
              Login to access your Estate Link account
            </Text>
          </View>

          {/* Form Section */}
          <View>
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              Password
            </Text>

            <TextInput
              className={`rounded-lg border bg-background-input text-text-primary ${
                showPasswordError ? 'border-red-500' : 'border-border'
              }`}
              style={{
                height: 48,
                borderWidth: 1,
                paddingHorizontal: 16,
                fontSize: 14,
                marginBottom: showPasswordError ? 8 : 24,
                fontFamily: 'Oxanium-Medium',
              }}
              placeholder="Enter your password"
              placeholderTextColor="#9CA3AF"
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
              onBlur={() => handleBlur('password')}
              returnKeyType="done"
              onSubmitEditing={handleLogin}
              blurOnSubmit={true}
              autoCapitalize="none"
              autoCorrect={false}
              secureTextEntry={true}
              editable={!isLoading}
            />

            {/* Error Message */}
            {showPasswordError && (
              <Text
                className="text-red-500 font-oxanium"
                style={{ fontSize: 12, marginBottom: 16 }}>
                {passwordError}
              </Text>
            )}

            {/* Global Error Message */}
            {error && (
              <View style={{ marginBottom: 16 }}>
                <ErrorMessage message={error} visible={true} />
              </View>
            )}

            {/* Login Button */}
            <TouchableOpacity
              className={`rounded-lg ${
                isFormValid && !isLoading
                  ? 'bg-primary'
                  : 'bg-gray-300'
              }`}
              style={{
                height: 48,
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 16,
              }}
              onPress={handleLogin}
              disabled={!isFormValid || isLoading}>
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text
                  className="font-oxanium-bold text-white"
                  style={{ fontSize: 16, fontWeight: '600' }}>
                  Login
                </Text>
              )}
            </TouchableOpacity>

            {/* Forgot Password Link */}
            <TouchableOpacity
              onPress={handleForgotPassword}
              style={{ alignItems: 'center', marginBottom: 16 }}>
              <Text
                className="font-oxanium text-primary"
                style={{ fontSize: 14, fontWeight: '500' }}>
                Forgot Password?
              </Text>
            </TouchableOpacity>

            {/* Help Text */}
            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 14, fontWeight: '400' }}>
              Enter your password to access your account
            </Text>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}
