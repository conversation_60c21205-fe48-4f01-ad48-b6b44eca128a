import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StatusBar,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import type { RouteProp } from '@react-navigation/native';
import { ErrorMessage } from 'components';
import { useAuth } from 'hooks/useAuth';
import { useFormValidation } from 'hooks/useFormValidation';
import * as yup from 'yup';

type RootStackParamList = {
  SetPassword: { userId: string; authenticator: string };
  Login: { authenticator: string };
  Dashboard: undefined;
};

type SetPasswordScreenNavigationProp = StackNavigationProp<RootStackParamList, 'SetPassword'>;
type SetPasswordScreenRouteProp = RouteProp<RootStackParamList, 'SetPassword'>;

export function SetPasswordScreen() {
  const navigation = useNavigation<SetPasswordScreenNavigationProp>();
  const route = useRoute<SetPasswordScreenRouteProp>();
  const { setPasswordUser, isLoading, error, clearMessages } = useAuth();

  const [formData, setFormData] = useState({
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const {
    errors,
    validateForm,
    setFieldTouched,
    getFieldError,
    isFieldTouched,
    clearErrors,
  } = useFormValidation(
    yup.object({
      oldPassword: yup.string().required('Old password is required'),
      newPassword: yup.string()
        .required('New password is required')
        .min(6, 'Password must be at least 6 characters')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
      confirmPassword: yup.string()
        .required('Please confirm your password')
        .oneOf([yup.ref('newPassword')], 'Passwords must match'),
    })
  );

  // Get params from route
  const { userId, authenticator } = route.params;

  const handleInputChange = async (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (error) {
      clearMessages();
    }

    // Validate field on change
    if (isFieldTouched(field as keyof typeof formData)) {
      const fieldError = await validateForm({ ...formData, [field]: value });
      if (fieldError.isValid) {
        // Clear field error if validation passes
      }
    }
  };

  const handleBlur = (field: string) => {
    setFieldTouched(field as keyof typeof formData, true);
  };

  const handleSetPassword = async () => {
    // Clear previous errors
    clearErrors();
    clearMessages();

    // Validate form
    const validation = await validateForm(formData);
    if (!validation.isValid) {
      return;
    }

    try {
      console.log('Setting password for user:', userId);
      
      const result = await setPasswordUser(
        userId,
        formData.newPassword,
        formData.oldPassword,
        formData.confirmPassword
      );
      
      if (result) {
        Alert.alert(
          'Success',
          'Password set successfully! You can now login.',
          [
            {
              text: 'OK',
              onPress: () => navigation.navigate('Login', { authenticator })
            }
          ]
        );
      }
    } catch (error: any) {
      console.error('Set password error:', error);
      Alert.alert('Error', error.message || 'Failed to set password');
    }
  };

  const oldPasswordError = getFieldError('oldPassword');
  const newPasswordError = getFieldError('newPassword');
  const confirmPasswordError = getFieldError('confirmPassword');
  
  const showOldPasswordError = isFieldTouched('oldPassword') && oldPasswordError;
  const showNewPasswordError = isFieldTouched('newPassword') && newPasswordError;
  const showConfirmPasswordError = isFieldTouched('confirmPassword') && confirmPasswordError;
  
  const isFormValid = formData.oldPassword.trim() && 
                     formData.newPassword.trim() && 
                     formData.confirmPassword.trim() && 
                     !oldPasswordError && 
                     !newPasswordError && 
                     !confirmPasswordError;

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Status Bar Area */}
        <View className="h-11 flex-row items-center justify-between px-6 pb-2 pt-32"></View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-10">
          {/* Logo Section */}
          <View className="items-center" style={{ marginBottom: 60 }}>
            <Image
              source={require('../../../assets/Logo.png')}
              style={{ width: 200, height: 64, marginBottom: 50 }}
              resizeMode="contain"
            />

            <Text
              className="text-center font-oxanium-bold text-text-primary"
              style={{
                fontSize: 30,
                fontWeight: '500',
                marginBottom: 8,
                lineHeight: 30,
                letterSpacing: 0.3,
              }}>
              Set Your Password
            </Text>

            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 16, fontWeight: '500' }}>
              Create a secure password for your account
            </Text>
          </View>

          {/* Form Section */}
          <View>
            {/* Old Password */}
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              Old Password
            </Text>

            <TextInput
              className={`rounded-lg border bg-background-input text-text-primary ${
                showOldPasswordError ? 'border-red-500' : 'border-border'
              }`}
              style={{
                height: 48,
                borderWidth: 1,
                paddingHorizontal: 16,
                fontSize: 14,
                marginBottom: showOldPasswordError ? 8 : 16,
                fontFamily: 'Oxanium-Medium',
              }}
              placeholder="Enter your old password"
              placeholderTextColor="#9CA3AF"
              value={formData.oldPassword}
              onChangeText={(value) => handleInputChange('oldPassword', value)}
              onBlur={() => handleBlur('oldPassword')}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />

            {showOldPasswordError && (
              <Text
                className="text-red-500 font-oxanium"
                style={{ fontSize: 12, marginBottom: 16 }}>
                {oldPasswordError}
              </Text>
            )}

            {/* New Password */}
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              New Password
            </Text>

            <TextInput
              className={`rounded-lg border bg-background-input text-text-primary ${
                showNewPasswordError ? 'border-red-500' : 'border-border'
              }`}
              style={{
                height: 48,
                borderWidth: 1,
                paddingHorizontal: 16,
                fontSize: 14,
                marginBottom: showNewPasswordError ? 8 : 16,
                fontFamily: 'Oxanium-Medium',
              }}
              placeholder="Enter your new password"
              placeholderTextColor="#9CA3AF"
              value={formData.newPassword}
              onChangeText={(value) => handleInputChange('newPassword', value)}
              onBlur={() => handleBlur('newPassword')}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />

            {showNewPasswordError && (
              <Text
                className="text-red-500 font-oxanium"
                style={{ fontSize: 12, marginBottom: 16 }}>
                {newPasswordError}
              </Text>
            )}

            {/* Confirm Password */}
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              Confirm Password
            </Text>

            <TextInput
              className={`rounded-lg border bg-background-input text-text-primary ${
                showConfirmPasswordError ? 'border-red-500' : 'border-border'
              }`}
              style={{
                height: 48,
                borderWidth: 1,
                paddingHorizontal: 16,
                fontSize: 14,
                marginBottom: showConfirmPasswordError ? 8 : 24,
                fontFamily: 'Oxanium-Medium',
              }}
              placeholder="Confirm your new password"
              placeholderTextColor="#9CA3AF"
              value={formData.confirmPassword}
              onChangeText={(value) => handleInputChange('confirmPassword', value)}
              onBlur={() => handleBlur('confirmPassword')}
              secureTextEntry={true}
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />

            {showConfirmPasswordError && (
              <Text
                className="text-red-500 font-oxanium"
                style={{ fontSize: 12, marginBottom: 16 }}>
                {confirmPasswordError}
              </Text>
            )}

            {/* Global Error Message */}
            {error && (
              <View style={{ marginBottom: 16 }}>
                <ErrorMessage message={error} visible={true} />
              </View>
            )}

            {/* Set Password Button */}
            <TouchableOpacity
              className={`rounded-lg ${
                isFormValid && !isLoading
                  ? 'bg-primary'
                  : 'bg-gray-300'
              }`}
              style={{
                height: 48,
                justifyContent: 'center',
                alignItems: 'center',
                marginBottom: 16,
              }}
              onPress={handleSetPassword}
              disabled={!isFormValid || isLoading}>
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text
                  className="font-oxanium-bold text-white"
                  style={{ fontSize: 16, fontWeight: '600' }}>
                  Set Password
                </Text>
              )}
            </TouchableOpacity>

            {/* Help Text */}
            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 14, fontWeight: '400' }}>
              Your password must be at least 6 characters and contain uppercase, lowercase, and numbers
            </Text>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
} 