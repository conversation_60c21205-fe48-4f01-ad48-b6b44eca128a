import { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StatusBar,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import { EyeIcon, ErrorMessage } from 'components';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { setPassword, clearError } from 'store/slices/authSlice';

type RootStackParamList = {
  Login: undefined;
  PasswordReset: undefined;
  ForgotPassword: undefined;
  InitialScreen: undefined;
  VerifyCode: undefined;
};

type PasswordResetScreenNavigationProp = StackNavigationProp<RootStackParamList, 'PasswordReset'>;

export function PasswordReset() {
  const navigation = useNavigation<PasswordResetScreenNavigationProp>();
  const dispatch = useAppDispatch();
  const { isLoading, error, user } = useAppSelector((state) => state.auth);
  
  const [formData, setFormData] = useState({
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    rememberMe: false,
  });

  const [showPasswords, setShowPasswords] = useState({
    oldPassword: false,
    newPassword: false,
    confirmPassword: false,
  });

  const [fieldErrors, setFieldErrors] = useState({
    oldPassword: false,
    newPassword: false,
    confirmPassword: false,
    passwordMismatch: false,
  });

  const [localError, setLocalError] = useState({
    message: '',
    visible: false,
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear field error when user starts typing
    if (fieldErrors[field as keyof typeof fieldErrors]) {
      setFieldErrors((prev) => ({ ...prev, [field]: false }));
    }
    // Clear password mismatch error when either password field changes
    if (field === 'newPassword' || field === 'confirmPassword') {
      setFieldErrors((prev) => ({ ...prev, passwordMismatch: false }));
    }
    // Clear general error when user starts typing
    if (localError.visible) {
      setLocalError({ message: '', visible: false });
    }
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const handleResetPassword = async () => {
    console.log('Reset Password button pressed');
    // Clear previous errors
    setLocalError({ message: '', visible: false });
    dispatch(clearError());
    setFieldErrors({
      oldPassword: false,
      newPassword: false,
      confirmPassword: false,
      passwordMismatch: false,
    });

    let hasFieldErrors = false;

    // Field validation
    if (!formData.oldPassword.trim()) {
      setFieldErrors((prev) => ({ ...prev, oldPassword: true }));
      hasFieldErrors = true;
    }

    if (!formData.newPassword.trim()) {
      setFieldErrors((prev) => ({ ...prev, newPassword: true }));
      hasFieldErrors = true;
    }

    if (!formData.confirmPassword.trim()) {
      setFieldErrors((prev) => ({ ...prev, confirmPassword: true }));
      hasFieldErrors = true;
    }

    if (hasFieldErrors) {
      return;
    }

    // Additional validation
    if (formData.newPassword.length < 8) {
      setLocalError({ message: 'New password must be at least 8 characters long', visible: true });
      return;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setFieldErrors((prev) => ({ ...prev, passwordMismatch: true }));
      return;
    }

    if (formData.oldPassword === formData.newPassword) {
      setLocalError({ message: 'New password must be different from old password', visible: true });
      return;
    }

    try {
      // Get user ID from Redux state
      const userId = user?.id;
      if (!userId) {
        setLocalError({ 
          message: 'User information not found. Please try again.', 
          visible: true 
        });
        return;
      }

      // Call the set password API
      await dispatch(setPassword({
        userId,
        oldPassword: formData.oldPassword,
        newPassword: formData.newPassword,
      })).unwrap();

      console.log('Password reset successful');
      
      // Navigate back to Login screen after successful password reset
      navigation.replace('Login');
      
    } catch (error) {
      // Error handling is done in Redux slice
      console.error('Password reset error:', error);
    }
  };

  const isFormValid =
    formData.oldPassword.trim() && formData.newPassword.trim() && formData.confirmPassword.trim();

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Status Bar Area */}
        <View className="h-11 flex-row items-center justify-between px-6 pb-2 pt-3"></View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-10">
          {/* Logo Section */}
          <View className="items-center" style={{ marginBottom: 60 }}>
            <Image
              source={require('../../../assets/Logo.png')}
              style={{ width: 200, height: 64, marginBottom: 40 }}
              resizeMode="contain"
            />

            <Text
              className="text-center font-oxanium-bold text-text-primary"
              style={{
                fontSize: 30,
                fontWeight: '500',
                marginBottom: 8,
                lineHeight: 30,
                letterSpacing: 0.3,
              }}>
              Welcome Back
            </Text>

            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 16, fontWeight: '500' }}>
              Login to access your Estate Link account
            </Text>
          </View>

          {/* Error Message */}
          <ErrorMessage message={localError.message} visible={localError.visible} />

          {/* Redux Error Message */}
          {error && (
            <ErrorMessage message={error} visible={true} />
          )}

          {/* Form Section */}
          <View>
            {/* Old Password */}
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '400', marginBottom: 8 }}>
              Old Password
            </Text>

            <View className="relative" style={{ marginBottom: fieldErrors.oldPassword ? 8 : 24 }}>
              <TextInput
                className="rounded-lg border-border bg-background-input text-text-primary"
                style={{
                  height: 48,
                  borderWidth: 1,
                  paddingHorizontal: 16,
                  paddingRight: 50,
                  fontSize: 14,
                  fontFamily: 'Oxanium-Medium',
                  fontWeight: '400',
                  letterSpacing: showPasswords.oldPassword ? 0 : 1,
                }}
                placeholder="enter old password"
                placeholderTextColor="#9CA3AF"
                value={formData.oldPassword}
                onChangeText={(value) => handleInputChange('oldPassword', value)}
                secureTextEntry={!showPasswords.oldPassword}
                autoCapitalize="none"
                autoCorrect={false}
                textContentType="password"
              />
              <EyeIcon
                isVisible={showPasswords.oldPassword}
                onPress={() => togglePasswordVisibility('oldPassword')}
                style={{ position: 'absolute', right: 16, top: 12 }}
              />
            </View>

            <ErrorMessage message="Old password is required" visible={fieldErrors.oldPassword} />

            {/* New Password */}
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              New Password
            </Text>

            <View className="relative" style={{ marginBottom: fieldErrors.newPassword ? 8 : 24 }}>
              <TextInput
                className="rounded-lg border-border bg-background-input text-text-primary"
                style={{
                  height: 48,
                  borderWidth: 1,
                  paddingHorizontal: 16,
                  paddingRight: 50,
                  fontSize: 14,
                  fontFamily: 'Oxanium-Medium',
                  fontWeight: '400',
                  letterSpacing: showPasswords.newPassword ? 0 : 1,
                }}
                placeholder="enter new password"
                placeholderTextColor="#9CA3AF"
                value={formData.newPassword}
                onChangeText={(value) => handleInputChange('newPassword', value)}
                secureTextEntry={!showPasswords.newPassword}
                autoCapitalize="none"
                autoCorrect={false}
                textContentType="newPassword"
              />
              <EyeIcon
                isVisible={showPasswords.newPassword}
                onPress={() => togglePasswordVisibility('newPassword')}
                style={{ position: 'absolute', right: 16, top: 12 }}
              />
            </View>

            <ErrorMessage message="New password is required" visible={fieldErrors.newPassword} />

            {/* Re-type New Password */}
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              Re-type New Password
            </Text>

            <View
              className="relative"
              style={{ marginBottom: fieldErrors.confirmPassword ? 8 : 24 }}>
              <TextInput
                className="rounded-lg border-border bg-background-input text-text-primary"
                style={{
                  height: 48,
                  borderWidth: 1,
                  paddingHorizontal: 16,
                  paddingRight: 50,
                  fontSize: 14,
                  fontFamily: 'Oxanium-Medium',
                  fontWeight: '400',
                  letterSpacing: showPasswords.confirmPassword ? 0 : 1,
                }}
                placeholder="re-type new password"
                placeholderTextColor="#9CA3AF"
                value={formData.confirmPassword}
                onChangeText={(value) => handleInputChange('confirmPassword', value)}
                secureTextEntry={!showPasswords.confirmPassword}
                autoCapitalize="none"
                autoCorrect={false}
                textContentType="newPassword"
              />
              <EyeIcon
                isVisible={showPasswords.confirmPassword}
                onPress={() => togglePasswordVisibility('confirmPassword')}
                style={{ position: 'absolute', right: 16, top: 12 }}
              />
            </View>

            <ErrorMessage
              message="Please confirm your new password"
              visible={fieldErrors.confirmPassword}
            />

            <ErrorMessage
              message="Your passwords do not match."
              visible={fieldErrors.passwordMismatch}
            />

            {/* Remember Me Checkbox */}
            <TouchableOpacity
              className="flex-row items-center"
              style={{ marginBottom: 32, paddingVertical: 8 }}
              onPress={() => setFormData((prev) => ({ ...prev, rememberMe: !prev.rememberMe }))}
              activeOpacity={0.7}>
              <View
                className="items-center justify-center"
                style={{
                  width: 18,
                  height: 18,
                  borderWidth: 2,
                  borderColor: '#3C9D9B',
                  borderRadius: 4,
                  marginRight: 12,
                  backgroundColor: formData.rememberMe ? '#3C9D9B' : 'transparent',
                }}>
                {formData.rememberMe && (
                  <Text className="font-bold text-white" style={{ fontSize: 12 }}>
                    ✓
                  </Text>
                )}
              </View>
              <Text
                className="font-oxanium-bold text-text-primary"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Remember me
              </Text>
            </TouchableOpacity>

            {/* Reset Password Button */}
            <TouchableOpacity
              className="items-center justify-center"
              style={{
                width: '100%',
                height: 48,
                backgroundColor: isFormValid && !isLoading ? '#3C9D9B' : 'white',
                borderWidth: 1.5,
                borderColor: '#3C9D9B',
                borderRadius: 24,
                marginBottom: 20,
              }}
              onPress={handleResetPassword}
              disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator color="#3C9D9B" size="small" />
              ) : (
                <Text
                  className="font-oxanium-bold"
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: isFormValid ? 'white' : '#3C9D9B',
                  }}>
                  Reset Password
                </Text>
              )}
            </TouchableOpacity>

            {/* Forgot Password Link */}
            <View className="items-center" style={{ marginBottom: 60 }}>
              <TouchableOpacity onPress={() => navigation.navigate('ForgotPassword')}>
                <Text
                  className="font-oxanium-bold text-primary"
                  style={{ fontSize: 16, fontWeight: '600' }}>
                  Forgot Password?
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Bottom Link */}
          <View className="flex-1 items-center justify-end" style={{ paddingBottom: 40 }}>
            <TouchableOpacity>
              <Text
                className="font-oxanium-bold text-text-primary underline"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Log into Estate Control
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}
