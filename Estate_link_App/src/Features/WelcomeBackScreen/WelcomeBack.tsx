import { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StatusBar,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { ErrorMessage, EyeIcon } from 'components';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { loginUser, clearError } from 'store/slices/authSlice';

type RootStackParamList = {
  Login: undefined;
  WelcomeBack: undefined;
  ForgotPassword: undefined;
};

type WelcomeBackScreenNavigationProp = StackNavigationProp<RootStackParamList, 'WelcomeBack'>;

export function WelcomeBack() {
  const navigation = useNavigation<WelcomeBackScreenNavigationProp>();
  const dispatch = useAppDispatch();
  const { isLoading, error, user } = useAppSelector((state) => state.auth);
  
  const [formData, setFormData] = useState({
    password: '',
    rememberMe: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showError, setShowError] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    
    // Clear Redux error when user starts typing
    if (error) {
      dispatch(clearError());
    }
    
    if (showError && value.trim()) {
      setShowError(false);
    }
  };

  const handleLogin = async () => {
    if (!formData.password.trim()) {
      setShowError(true);
      return;
    }

    setShowError(false);
    dispatch(clearError());

    try {
      // Get username from Redux state (should be set from previous screen)
      const username = user?.username || user?.email || user?.phone || '';
      
      if (!username) {
        setShowError(true);
        return;
      }

      // Login with username and password
      await dispatch(loginUser({ 
        username, 
        password: formData.password 
      })).unwrap();
      
      // Navigate to main app or dashboard on success
      console.log('Login successful');
      // Add navigation to main app here
      
    } catch (error) {
      // Error handling is done in Redux slice
      console.error('Login error:', error);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Status Bar Area */}
        <View className="h-11 flex-row items-center justify-between px-6 pb-2 pt-32"></View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-10">
          {/* Logo Section */}
          <View className="items-center" style={{ marginBottom: 80 }}>
            <Image
              source={require('../../../assets/Logo.png')}
              style={{ width: 200, height: 64, marginBottom: 50 }}
              resizeMode="contain"
            />

            <Text
              className="text-center font-oxanium-bold text-text-primary"
              style={{
                fontSize: 30,
                fontWeight: '500',
                marginBottom: 8,
                lineHeight: 30,
                letterSpacing: 0.3,
              }}>
              Welcome Back
            </Text>

            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 16, fontWeight: '500' }}>
              Login to access your Estate Link account
            </Text>
          </View>

          {/* Form Section */}
          <View>
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              Password
            </Text>

            <View className="relative">
              <TextInput
                className="rounded-lg border-border bg-background-input text-text-primary pr-12"
                style={{
                  height: 48,
                  borderWidth: 1,
                  paddingHorizontal: 16,
                  fontSize: 14,
                  marginBottom: showError ? 8 : 24,
                  fontFamily: 'Oxanium-Medium',
                  fontWeight: '400',
                  letterSpacing: showPassword ? 0 : 1,
                }}
                placeholder="enter Password"
                placeholderTextColor="#9CA3AF"
                value={formData.password}
                onChangeText={(value) => handleInputChange('password', value)}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
                textContentType="password"
                returnKeyType="done"
                onSubmitEditing={handleLogin}
                blurOnSubmit={true}
              />
              
              <View className="absolute right-4 top-3">
                <EyeIcon isVisible={showPassword} onPress={togglePasswordVisibility} />
              </View>
            </View>

            <ErrorMessage
              message="Please enter your password to continue."
              visible={showError}
            />

            {/* Redux Error Message */}
            {error && (
              <ErrorMessage message={error} visible={true} />
            )}

            {/* Remember Me Checkbox */}
            <TouchableOpacity
              className="flex-row items-center"
              style={{ marginBottom: 32, paddingVertical: 8 }}
              onPress={() => setFormData((prev) => ({ ...prev, rememberMe: !prev.rememberMe }))}
              activeOpacity={0.7}>
              <View
                className="items-center justify-center"
                style={{
                  width: 18,
                  height: 18,
                  borderWidth: 2,
                  borderColor: '#3C9D9B',
                  borderRadius: 4,
                  marginRight: 12,
                  backgroundColor: formData.rememberMe ? '#3C9D9B' : 'transparent',
                }}>
                {formData.rememberMe && (
                  <Text className="font-bold text-white" style={{ fontSize: 12 }}>
                    ✓
                  </Text>
                )}
              </View>
              <Text
                className="font-oxanium-bold text-text-primary"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Remember me
              </Text>
            </TouchableOpacity>

            {/* Login Button */}
            <TouchableOpacity
              className="items-center justify-center"
              style={{
                width: '100%',
                height: 48,
                backgroundColor: formData.password.trim() && !isLoading ? '#3C9D9B' : 'white',
                borderWidth: 1.5,
                borderColor: '#3C9D9B',
                borderRadius: 24,
                marginBottom: 20,
              }}
              onPress={handleLogin}
              disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator color="#3C9D9B" size="small" />
              ) : (
                <Text
                  className="font-oxanium-bold"
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: formData.password.trim() ? 'white' : '#3C9D9B',
                  }}>
                  Login
                </Text>
              )}
            </TouchableOpacity>

            {/* Forgot Password Link */}
            <View className="items-center" style={{ marginBottom: 60 }}>
              <TouchableOpacity onPress={() => navigation.navigate('ForgotPassword')}>
                <Text
                  className="font-oxanium-bold text-primary"
                  style={{ fontSize: 16, fontWeight: '600' }}>
                  Forgot Password?
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Bottom Link */}
          <View className="flex-1 items-center justify-end" style={{ paddingBottom: 40 }}>
            <TouchableOpacity>
              <Text
                className="font-oxanium-bold text-text-primary underline"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Log into Estate Control
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}