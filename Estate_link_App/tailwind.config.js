/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: ['./App.tsx', './components/**/*.{js,jsx,ts,tsx}', './src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      fontFamily: {
        oxanium: ['Oxanium-Regular', 'sans-serif'],
        'oxanium-medium': ['Oxanium-Medium', 'sans-serif'],
        'oxanium-semibold': ['Oxanium-SemiBold', 'sans-serif'],
        'oxanium-bold': ['Oxanium-Bold', 'sans-serif'],
      },
      colors: {
        primary: {
          DEFAULT: '#3C9D9B',
          50: '#F0F9F9',
          100: '#CCEBEA',
          500: '#3C9D9B',
          600: '#2D7A78',
        },
        text: {
          primary: '#313131',
          secondary: '#656565',
          placeholder: '#9CA3AF',
        },
        border: {
          DEFAULT: '#D1D5DB',
          primary: '#3C9D9B',
        },
        background: {
          input: '#F9FAFB',
        },
        error: {
          DEFAULT: '#FF6B6B',
          light: '#FF8E8E',
          dark: '#E55A5A',
        },
        bg: {
          primary: '#3C9D9B',
        },
      },
    },
  },
  plugins: [],
};
