{"name": "my-expo-app", "version": "1.0.0", "scripts": {"android": "expo start --android", "ios": "expo start --ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@expo-google-fonts/oxanium": "^0.4.1", "@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.1.16", "@react-navigation/stack": "^7.4.4", "@reduxjs/toolkit": "^2.2.1", "expo": "^53.0.9", "expo-font": "^13.3.2", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "nativewind": "latest", "react": "19.0.0", "react-icons": "^5.5.0", "react-native": "0.79.2", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-redux": "^9.1.0", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.0", "typescript": "~5.8.3"}, "main": "node_modules/expo/AppEntry.js", "private": true}