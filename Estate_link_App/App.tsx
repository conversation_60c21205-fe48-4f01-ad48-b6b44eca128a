import './global.css';
import { Provider } from 'react-redux';
import { store } from './src/store';
import { Login } from './src/Features/LoginScreen/Login';
import { WelcomeBack } from './src/Features/WelcomeBackScreen/WelcomeBack';
import { InitialScreen } from './src/Features/InitialResetPassword/InitialScreen';
import { PasswordReset } from './src/Features/InitialResetPassword/PasswordReset';
import { ForgotPassword } from './src/Features/ForgetPasswordScreen/ForgotPassword';
import { VerifyCode } from './src/Features/VerifyCodeScreen/VerifyCode';
import { SetPassword } from './src/Features/SetPasswordScreen/SetPassword';
import {
  useFonts,
  Oxanium_400Regular,
  Oxanium_500Medium,
  Oxanium_600SemiBold,
  Oxanium_700Bold,
} from '@expo-google-fonts/oxanium';
import { View } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import { useCallback } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

const Stack = createStackNavigator();

export default function App() {
  const [fontsLoaded, fontError] = useFonts({
    'Oxanium-Regular': Oxanium_400Regular,
    'Oxanium-Medium': Oxanium_500Medium,
    'Oxanium-SemiBold': Oxanium_600SemiBold,
    'Oxanium-Bold': Oxanium_700Bold,
  });

  const onLayoutRootView = useCallback(async () => {
    if (fontsLoaded || fontError) {
      await SplashScreen.hideAsync();
    }
  }, [fontsLoaded, fontError]);

  if (!fontsLoaded && !fontError) {
    return null;
  }

  return (
    <Provider store={store}>
      <View className="flex-1 bg-white" onLayout={onLayoutRootView}>
        <NavigationContainer>
          <Stack.Navigator
            initialRouteName="InitialScreen"
            screenOptions={{
              headerShown: false,
            }}>
            <Stack.Screen name="Login" component={Login} />
            <Stack.Screen name="WelcomeBack" component={WelcomeBack} />
            <Stack.Screen name="InitialScreen" component={InitialScreen} />
            <Stack.Screen name="PasswordReset" component={PasswordReset} />
            <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
            <Stack.Screen name="VerifyCode" component={VerifyCode} />
            <Stack.Screen name="SetPassword" component={SetPassword} />
          </Stack.Navigator>
        </NavigationContainer>
      </View>
    </Provider>
  );
}
