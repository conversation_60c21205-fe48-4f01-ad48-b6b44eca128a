@echo off
echo ====================================
echo Starting Django Development Server
echo ====================================
echo.
echo This will start the Django server on 0.0.0.0:8000
echo making it accessible from:
echo   - Local: http://127.0.0.1:8000
echo   - Android Emulator: http://********:8000
echo   - iOS Simulator: http://localhost:8000
echo.
echo Press Ctrl+C to stop the server
echo ====================================
echo.

cd /d "%~dp0"

REM Check if manage.py exists
if not exist "manage.py" (
    echo Error: manage.py not found in current directory
    echo Please run this script from the backend directory
    pause
    exit /b 1
)

REM Start Django server on all interfaces
python manage.py runserver 0.0.0.0:8000

pause
