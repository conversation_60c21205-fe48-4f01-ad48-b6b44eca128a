from rest_framework import serializers
from django.db import transaction
from towers.models import Owner, OwnerDocs, Unit
from user.models import Member
from datetime import datetime
from user.serializers import MemberSerializer
import os

class OwnerSerializer(serializers.ModelSerializer):
    date_of_ownership = serializers.CharField(required=True)
    # owner_docs = serializers.ListField(
    #     child=serializers.FileField(allow_empty_file=False), required=False
    # )
    unit_name = serializers.SerializerMethodField()  
    owner_docs = serializers.SerializerMethodField()
    owner_docs_upload = serializers.ListField(
        child=serializers.FileField(allow_empty_file=False),
        required=False,
        write_only=True
    )
    docs_to_delete = serializers.ListField(
        child=serializers.IntegerField(), required=False, write_only=True
    )
    docs_to_update = serializers.ListField(
        child=serializers.DictField(), required=False, write_only=True
    )
    tower = serializers.SerializerMethodField()
    def get_tower(self, obj):
        # Traverse the relationships to get the tower
        tower = obj.unit.floor.tower
        if tower:
            return {
                'tower_name': tower.tower_name,
                'tower_number': tower.tower_number
            }
        
        return None
    def get_unit_name(self, obj):
        return obj.unit.unit_name if obj.unit else None
    def get_ownership_percentage(self, obj):
         
        return str(obj.ownership_percentage)
 
    def get_owner_docs(self, obj):
        request = self.context.get('request')
        docs = OwnerDocs.objects.filter(owner=obj)
        return [
            {
                'id': doc.id,
                'url': request.build_absolute_uri(doc.owner_docs.url) if doc.owner_docs else None,
                'name': os.path.basename(doc.owner_docs.name) if doc.owner_docs else None
            }
            for doc in docs
        ]



    class Meta:
        model = Owner
        fields = [
            'id', 'member', 'unit_name', 'unit', 'tower', 'ownership_percentage', 'date_of_ownership',
            'created_by', 'created_at', 'updated_by', 'updated_at',
            'owner_docs', 'owner_docs_upload', 'ownership_transfer_from', 'docs_to_delete', 'docs_to_update'
        ]
        read_only_fields = ['created_by', 'created_at', 'updated_by', 'updated_at']

    def validate_member(self, value):
        if isinstance(value, Member):
            return value
        try:
            return Member.objects.get(id=value)
        except Member.DoesNotExist:
            raise serializers.ValidationError("Member does not exist.")

    def validate_ownership_transfer_from(self, value):
        if value in (None, "", "null"):
            return None
        if isinstance(value, Member):
            return value
        try:
            return Member.objects.get(id=value)
        except Member.DoesNotExist:
            raise serializers.ValidationError("Ownership transfer member not found.")


    def validate_date_of_ownership(self, value):
        try:
            return datetime.strptime(value, "%d-%b-%Y").date()
        except ValueError:
            raise serializers.ValidationError("Date of ownership must be in format DD-MMM-YYYY (e.g., 01-Jan-2025)")

    def create(self, validated_data):
        owner_docs = validated_data.pop('owner_docs_upload', [])

        with transaction.atomic():
            user = self.context['request'].user
            if not user.is_authenticated:
                raise serializers.ValidationError("User must be authenticated.")

            member_creator = Member.objects.get(user=user)
            validated_data['created_by'] = member_creator
            validated_data['updated_by'] = member_creator
            # validated_data['updated_by'] = 'Available'

      
            owner = Owner.objects.create(**validated_data)

            for file in owner_docs:
                OwnerDocs.objects.create(
                    owner=owner,
                    unit=owner.unit,
                    owner_docs=file,
                    created_by=member_creator,
                    updated_by=member_creator
                )
            
            unit_id = validated_data.get('unit')
            unit = Unit.objects.get(id=unit_id.id if hasattr(unit_id, 'id') else unit_id)
            
            # Update unit status
            unit.unit_status = 'available'
            unit.status_color = unit.STATUS_COLORS['available']
            unit.save()
            
            return owner
        
        
    def update(self, instance, validated_data):
        owner_docs = validated_data.pop('owner_docs_upload', [])
        
        # Handle docs_to_delete - try multiple ways to get the data
        docs_to_delete = validated_data.pop('docs_to_delete', [])
        
        # If not in validated_data, try to get from request data
        if not docs_to_delete:
            request_data = self.context['request'].data
            print("🔍 Request data type:", type(request_data))
            print("🔍 Request data keys:", list(request_data.keys()) if hasattr(request_data, 'keys') else "No keys")
            
            # Try different possible formats
            if hasattr(request_data, 'getlist'):
                docs_to_delete = request_data.getlist('docs_to_delete[]', [])
            elif isinstance(request_data, dict):
                docs_to_delete = request_data.get('docs_to_delete[]', [])
                if not isinstance(docs_to_delete, list):
                    docs_to_delete = [docs_to_delete] if docs_to_delete else []
            else:
                # For FormData, try to get all values with this key
                docs_to_delete = []
                for key in request_data.keys():
                    if key == 'docs_to_delete[]':
                        value = request_data.get(key)
                        if value:
                            docs_to_delete.append(value)

        print("🔁 Updating Owner:", instance.id)
        print("📄 New files to upload:", len(owner_docs))
        print("🗑 Docs to delete:", docs_to_delete)

        with transaction.atomic():
            for attr, value in validated_data.items():
                setattr(instance, attr, value)

            instance.updated_by = Member.objects.get(user=self.context['request'].user)
            instance.save()

            if docs_to_delete:
                # Convert to integers and filter out any invalid values
                doc_ids = []
                for doc_id in docs_to_delete:
                    try:
                        doc_ids.append(int(doc_id))
                    except (ValueError, TypeError):
                        print(f"⚠️ Invalid doc_id: {doc_id}")
                        continue
                
                if doc_ids:
                    # Get the documents to delete
                    docs_to_remove = OwnerDocs.objects.filter(id__in=doc_ids, owner=instance)
                    
                    # Delete the actual files from media storage first
                    for doc in docs_to_remove:
                        if doc.owner_docs:
                            try:
                                # Delete the file from storage
                                doc.owner_docs.delete(save=False)
                                print(f"🗑️ Deleted file: {doc.owner_docs.name}")
                            except Exception as e:
                                print(f"⚠️ Error deleting file {doc.owner_docs.name}: {e}")
                    
                    # Then delete the database records
                    deleted = docs_to_remove.delete()
                    print(f"✅ Deleted {deleted[0]} docs with IDs: {doc_ids}")

            for file in owner_docs:
                OwnerDocs.objects.create(
                    owner=instance,
                    unit=instance.unit,
                    owner_docs=file,
                    created_by=instance.updated_by,
                    updated_by=instance.updated_by
                )

        return instance




class OwnerDetailSerializer(serializers.ModelSerializer):
    member = MemberSerializer(read_only=True)
    ownership_transfer_from = serializers.SerializerMethodField()
    docs = serializers.SerializerMethodField()

    class Meta:
        model = Owner
        fields = [
            'id', 'member', 'ownership_percentage', 'date_of_ownership',
            'created_at', 'updated_at', 'docs', 'ownership_transfer_from'
        ]

    def get_docs(self, obj):
        request = self.context.get('request')
        docs = OwnerDocs.objects.filter(owner=obj)
        return [
            {
                "id": doc.id,
                "url": request.build_absolute_uri(doc.owner_docs.url) if doc.owner_docs and request else doc.owner_docs.url if doc.owner_docs else None
            }
            for doc in docs if doc.owner_docs
        ]


    def get_ownership_transfer_from(self, obj):
        if obj.ownership_transfer_from:
            return {
                "id": obj.ownership_transfer_from.id,
                "full_name": obj.ownership_transfer_from.full_name
            }
        return None

class MemberUnitOwnershipSerializer(serializers.ModelSerializer):
    unit_name = serializers.CharField(source='unit.unit_name')
    tower_name = serializers.CharField(source='unit.floor.tower.tower_name')
    tower_number = serializers.IntegerField(source='unit.floor.tower.tower_number')
    unit_id = serializers.IntegerField(source='unit.id')
    docs = serializers.SerializerMethodField()

    class Meta:
        model = Owner
        fields = [
            'unit_name', 'tower_name', 'tower_number', 'unit_id',
            'ownership_percentage', 'date_of_ownership', 'docs'
        ]

    def get_docs(self, obj):
        request = self.context.get('request')
        docs = OwnerDocs.objects.filter(owner=obj)
        return [
            request.build_absolute_uri(doc.owner_docs.url) if doc.owner_docs and request else doc.owner_docs.url 
            for doc in docs if doc.owner_docs
        ]
