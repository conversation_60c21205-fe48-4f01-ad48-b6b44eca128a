from rest_framework import generics
from towers.models import Tower,Unit,Floor,UnitStaff
from towers.serializers.tower_serializers import TowerSerializer,UnitSideDetailSerializer,UnitSerializer
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import NotFound
from user.permissions import HasRequiredPermission
from django.shortcuts import get_object_or_404
from user.models import Member
from django.http import JsonResponse
from towers.models import Resident,Owner
from django.db.models import Q

from user.serializers import MemberSerializer

class GetLastTowerNumber(APIView):
    """
    Get the last tower number from the database and return (last_tower_number + 1)
    """
    permission_classes = [IsAuthenticated,HasRequiredPermission]
    required_permission_id = [10]

    def get(self, request):
        last_tower = Tower.objects.order_by("-tower_number").first()
        last_tower_number = last_tower.tower_number if last_tower else 0  # If no towers exist, start from 1
        return Response({"lastTowerNumber": last_tower_number + 1}, status=status.HTTP_200_OK)

class CreateTower(APIView):
    permission_classes = [IsAuthenticated,HasRequiredPermission]
    required_permission_id = [10]
    def post(self, request):
        print(request.data)
        serializer = TowerSerializer(data=request.data,context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Tower created successfully"}, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TowerList(APIView):
    permission_classes = [IsAuthenticated,HasRequiredPermission]
    required_permission_id = [12]
    def get(self, request):
        towers = Tower.objects.all()
        serializer = TowerSerializer(towers,many=True)
        return Response(serializer.data)

class UpdateTower(APIView):
    permission_classes = [IsAuthenticated,HasRequiredPermission]
    required_permission_id = [11]

    def put(self, request, pk):
        try:
            # Fetch the tower instance to update
            tower = Tower.objects.get(id=pk)
        except Tower.DoesNotExist:
            return Response({"detail": "Tower not found."}, status=status.HTTP_404_NOT_FOUND)
        
        # Get all floors associated with the tower
        floors = Floor.objects.filter(tower=tower)

        for floor in floors:
            units = Unit.objects.filter(floor=floor)
            for unit in units:
                if unit.updated_by is not None:
                    return Response(
                        {"detail": f"Unit No: {unit.unit_name} on Floor No: {floor.floor_no} has already been updated by an user."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                elif Owner.objects.filter(unit=unit).exists():
                    return Response(
                        {"detail": f"An owner is already associated with Unit No: {unit.unit_name} in this tower."},
                        status=status.HTTP_400_BAD_REQUEST
                    ) 
                elif Resident.objects.filter(unit=unit).exists():
                    return Response(
                        {"detail": f"A resident is already associated with Unit No: {unit.unit_name} in this tower."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                elif UnitStaff.objects.filter(unit=unit).exists():
                    return Response(
                        {"detail": f"Unit staff is already associated with Unit No: {unit.unit_name} in this tower."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

        print(request.data) 

        # Pass the tower instance and the request context to the serializer
        serializer = TowerSerializer(tower, data=request.data, context={'request': request}, partial=True)

        # Check if the provided data is valid
        if serializer.is_valid():
            # Save the updated tower instance
            serializer.save()
            return Response({"message": "Tower updated successfully"}, status=status.HTTP_200_OK)

        # If there are validation errors, return them
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class TowerDetails(APIView):
    permission_classes = [IsAuthenticated,HasRequiredPermission]
    required_permission_id = [11]

    def get(self, request, pk):
        try:
            # Retrieve the user profile by primary key (pk)
            tower_profile = Tower.objects.get(pk=pk)
        except Tower.DoesNotExist:
            raise NotFound("Tower not found")

        serializer = TowerSerializer(tower_profile)
        return Response(serializer.data)

class DeleteTower(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, pk):
        try:
            # Retrieve the tower by primary key (pk)
            tower_profile = Tower.objects.get(pk=pk)
        except Tower.DoesNotExist:
            raise NotFound("Tower not found")
        floors = Floor.objects.filter(tower=tower_profile)

        for floor in floors:
            units = Unit.objects.filter(floor=floor)
            for unit in units:
                if unit.updated_by is not None:
                    return Response(
                        {"message": f"Unit No: {unit.unit_name} on Floor No: {floor.floor_no} has already been updated by an user."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                elif Owner.objects.filter(unit=unit).exists():
                    return Response(
                        {"message": f"An owner is already associated with Unit No: {unit.unit_name} in this tower."},
                        status=status.HTTP_400_BAD_REQUEST
                    ) 
                elif Resident.objects.filter(unit=unit).exists():
                    return Response(
                        {"message": f"A resident is already associated with Unit No: {unit.unit_name} in this tower."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                elif UnitStaff.objects.filter(unit=unit).exists():
                    return Response(
                        {"message": f"Unit staff is already associated with Unit No: {unit.unit_name} in this tower."},
                        status=status.HTTP_400_BAD_REQUEST
                    )

        # Delete the tower
        tower_profile.delete()
        return Response({"message": "Tower deleted successfully"}, status=status.HTTP_204_NO_CONTENT)
    
class UnitSideDetails(APIView):
    def get(self, request, pk):
        
        unit = get_object_or_404(Unit, pk=pk)
        
        
        serializer = UnitSideDetailSerializer(unit)
        
        
        return Response(serializer.data)

class UpdateUnit(APIView):
    def put(self, request, pk):
        print(request.data) 

        try:
            # Fetch the tower instance to update
            unit = Unit.objects.get(id=pk)
        except Unit.DoesNotExist:
            return Response({"detail": "Unit not found."}, status=status.HTTP_404_NOT_FOUND)


        # Pass the tower instance and the request context to the serializer
        serializer = UnitSerializer(unit, data=request.data, context={'request': request}, partial=True)

        # Check if the provided data is valid
        if serializer.is_valid():
            # Save the updated tower instance
            serializer.save()
            return Response({"message": "Unit updated successfully"}, status=status.HTTP_200_OK)

        # If there are validation errors, return them
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# class UnitDetails(APIView):

#     def get(self, request, pk):
#         try:
#             # Retrieve the user profile by primary key (pk)
#             unit_profile = Unit.objects.get(pk=pk)
#         except Tower.DoesNotExist:
#             raise NotFound("Tower not found")

#         serializer = UnitSerializer(unit_profile)
#         return Response(serializer.data)
class UnitDetails(APIView):
    def get(self, request, pk):
        try:
            unit_profile = Unit.objects.prefetch_related('docs').get(pk=pk)
        except Unit.DoesNotExist:
            raise NotFound("Unit not found")

        serializer = UnitSerializer(unit_profile, context={'request': request})
        return Response(serializer.data)
 
class AddExistingMemberForOwner(APIView):

    def get(self, request, format=None):
        # Retrieve all members where is_org_member=True OR is_comm_member=True
        members = Member.objects.filter(Q(is_org_member=True) | Q(is_comm_member=True))
        members_serializer = MemberSerializer(members, many=True)

        # print("hello",len(members_serializer.data))

        return Response({ "org_members": members_serializer.data})


class AddExistingMemberForContact(APIView):
    def get(self, request, pk):
        # Retrieve the Unit object or return a 404 if not found.
        unit = get_object_or_404(Unit, id=pk)

        # --- Process Owner Contacts ---
        owner_qs = Owner.objects.filter(unit_id=pk)
        owner_member_ids = list(owner_qs.values_list('member', flat=True))
        owner_members = Member.objects.filter(id__in=owner_member_ids)
        serialized_owner_members = MemberSerializer(owner_members, many=True)

        # Remove unwanted keys and mark them as owners.
        owners_list = []
        for owner_data in serialized_owner_members.data:
            owner_data.pop("member_roles", None)
            owner_data.pop("member_groups", None)
            owner_data.pop("member_type_edit", None)
            owner_data["contact_type"] = "owner"
            owners_list.append(owner_data)

        # --- Process Resident Contacts ---
        resident_qs = Resident.objects.filter(unit_id=pk)
        resident_member_ids = list(resident_qs.values_list('member', flat=True))
        resident_members = Member.objects.filter(id__in=resident_member_ids)
        serialized_resident_members = MemberSerializer(resident_members, many=True)

        # Remove unwanted keys and mark them as residents.
        residents_list = []
        for resident_data in serialized_resident_members.data:
            resident_data.pop("member_roles", None)
            resident_data.pop("member_groups", None)
            resident_data.pop("member_type_edit", None)
            resident_data["contact_type"] = "resident"
            residents_list.append(resident_data)

        # --- Additional Unit Information ---
        # For the unit_owner_name, we'll pick the first owner's member full_name, if any.
        owner_instance = owner_qs.first()
        unit_owner_name = owner_instance.member.full_name if owner_instance else None

        # Get floor name (assuming floor_no is descriptive) and tower name if available.
        floor_name = unit.floor.floor_no if unit.floor else None
        tower_name = unit.floor.tower.tower_name if hasattr(unit.floor, 'tower') and unit.floor.tower else None

        # Prepare the composite response payload.
        response_data = {
            "owners": owners_list,
            "residents": residents_list,
            "unit_owner_name": unit_owner_name,
            "floor_name": floor_name,
            "tower_name": tower_name,
            "unit_name": unit.unit_name,
        }
        
        return Response(response_data)
        

