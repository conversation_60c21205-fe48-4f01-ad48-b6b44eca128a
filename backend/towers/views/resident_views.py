
from towers.serializers.resident_serializers import AddExistingUnit<PERSON>taff,ResidentUpdateSerializer,ResidentSerializer,AddExistingResident,AddExistingOwners,ResidentMemberSerializer
from towers.serializers.unitStaff_serializers import UnitStaffSerializer
from user.serializers import MemberSerializer
from django.core.mail import send_mail
from django.conf import settings
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import NotFound
from user.permissions import HasRequiredPermission
# from django.shortcuts import get_object_or_404
from rest_framework.views import APIView
from rest_framework.response import Response
from towers.models import Resident,ResidentDocs,Owner,UnitStaff, Unit
from group_role.models import MembersRole
from user.models import Member
from django.http import JsonResponse
import logging
from user.serializers import MemberSerializer
from django.db.models import Q
from audit_trail.create_audit_trail import create_audit_trail
from django.shortcuts import get_object_or_404,get_list_or_404
from django.db import transaction


logger = logging.getLogger(__name__)
class CreateResident(APIView):
    # permission_classes = [IsAuthenticated, HasRequiredPermission]
    # required_permission_id = [11]
    def post(self, request):
        serializer = ResidentSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            resident = serializer.save()
            return Response(
                {"message": "Resident created successfully", "data": serializer.data},
                status=status.HTTP_201_CREATED
            )
        # The serializer.errors will now include our custom duplicate email error message.
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    

# class AddExtingMemberForResident(APIView):
#     def get(self, request, format=None):
#         # Retrieve all residents
#         owners = Owner.objects.all()
#         owner_serializer = AddExistingMemberForResident(owners, many=True)
        
#         # Retrieve all members whose is_org_member field is True (1)
#         org_members = Member.objects.filter(is_org_member=True)
#         org_member_serializer = MemberSerializer(org_members, many=True)
        
#         # Return both lists in a combined response
#         return Response({
#             "owners": owner_serializer.data,
#             "org_members": org_member_serializer.data,
#         })


class AddExistingMemberView(APIView):
    """
    API view that returns owners, organization members, and resident members.
    """

    def get(self, request, format=None):
        # Retrieve all owners and serialize them
        owners = Owner.objects.all()
        owner_serializer = AddExistingOwners(owners, many=True)
        
        # Retrieve organization members (those with is_org_member True) and serialize them
        org_members = Member.objects.filter(is_org_member=True)
        org_member_serializer = MemberSerializer(org_members, many=True)
        
        # Retrieve resident records (distinct by member id)
        residents_qs = Resident.objects.all().order_by('id')
        seen_member_ids = set()
        distinct_residents = []
        for resident in residents_qs:
            if resident.member.id not in seen_member_ids:
                seen_member_ids.add(resident.member.id)
                distinct_residents.append(resident)
                
        resident_serializer = AddExistingResident(distinct_residents, many=True)

        unit_staff = UnitStaff.objects.all()
        unit_staff_serializer = AddExistingUnitStaff(unit_staff, many=True)
        # Return the combined response
        return Response({
            "owners": owner_serializer.data,
            "org_members": org_member_serializer.data,
            "resident_members": resident_serializer.data,
            "unit_staff": unit_staff_serializer.data
        })

# class ListSearchSort(APIView):
#     def get(self, request):
       
#         # Expecting member_type to be a list that may include numeric ids and/or the string "owner"
#         member_type_filters = request.GET.getlist('member_type', [])
        
#         # Determine if 'owner' filter is applied and extract numeric member type ids
#         owner_selected = "owner" in member_type_filters
#         numeric_member_type_ids = []
#         for mt in member_type_filters:
#             if mt != "owner":
#                 try:
#                     numeric_member_type_ids.append(int(mt))
#                 except ValueError:
#                     logger.warning(f"Invalid member_type value skipped: {mt}")  
        
#         # ===== Initialize Querysets =====
#         member_type_members = Member.objects.none()
#         owner_members = Member.objects.none()
        
#         # ===== Apply Numeric Member Type Filter =====
#         if numeric_member_type_ids:
#             member_type_members = Member.objects.filter(member_type__id__in=numeric_member_type_ids)
        
#         # ===== Apply Owner Filter =====
#         if owner_selected:
#             owner_member_ids = Owner.objects.values_list('member', flat=True).distinct()
#             owner_members = Member.objects.filter(id__in=owner_member_ids)
        
#         # ===== If No Filters, Return All Owners and Org Members =====
#         if not member_type_filters:
#             all_owners = Owner.objects.all()
#             owners_serializer = AddExistingMemberForResident(all_owners, many=True)
    
#             org_members = Member.objects.filter(is_org_member=True)
#             org_members_serializer = MemberSerializer(org_members, many=True, context={'request': request})
    
#             return Response({
#                 "owners": owners_serializer.data,
#                 "org_members": org_members_serializer.data
#             })
        
#         # ===== Serialize Filtered Results =====
#         member_type_serializer = MemberSerializer(member_type_members, many=True, context={'request': request})
#         owner_serializer = MemberSerializer(owner_members, many=True, context={'request': request})
        
#         # ===== Return Filtered Response =====
#         return Response({
#             "org_members": member_type_serializer.data,
#             "owners": owner_serializer.data

#         })
class ResidentMemberList(APIView):
    # permission_classes = [IsAuthenticated]

    def get(self, request, unit_pk):

        residents = Resident.objects.select_related('member', 'unit').filter(
            unit__id=unit_pk,
            is_active=True,
            member__is_comm_member=True  # Only include active community members
        )
        serializer = ResidentMemberSerializer(residents, many=True)
        print('Filtered residents count:', len(serializer.data))
        return Response(serializer.data)
      
class ResidentDetails(APIView):
    # permission_classes = [IsAuthenticated]
    def get(self, request, unit_id, resident_id):
        try:
            # Filter using the resident's primary key and its associated unit
            resident = Resident.objects.get(id=resident_id, unit__id=unit_id)
        except Resident.DoesNotExist:
            return Response(
                {"error": "Resident not found for the specified unit and resident id."},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = ResidentSerializer(resident)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
class ResidentInfoEdit(APIView):
    # If you wish to require authentication, uncomment the next line.
    # permission_classes = [IsAuthenticated]

    def put(self, request, resident_id):
        try:
            resident = Resident.objects.get(id=resident_id)
        except Resident.DoesNotExist:
            return Response({"error": "Resident not found."}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = ResidentUpdateSerializer(instance=resident, data=request.data, partial=True, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
 
class InactivateResidents(APIView):
    # permission_classes = [IsAuthenticated]   
    def post(self, request, *args, **kwargs):
        resident_ids = request.data.get('resident_ids', [])
        if not resident_ids or not isinstance(resident_ids, list):
            return Response({"error": "resident_ids must be provided as a list."}, status=status.HTTP_400_BAD_REQUEST)

        updated_count = 0
        for res_id in resident_ids:
            try:
                resident = Resident.objects.get(id=res_id)
                 
                if resident.is_active:
                    resident.is_active = False
                    resident.save()
                    updated_count += 1

                    # Send email notification to the resident's general email.
                    recipient_email = resident.member.general_email
                    subject = "Account Deactivation"
                    message = "Your account has been deactivated."
                    send_mail(
                        subject,
                        message,
                        settings.EMAIL_HOST_USER,
                        [recipient_email],
                        fail_silently=False
                    )
            except Resident.DoesNotExist:
                logger.warning(f"Resident  not found.")
                continue
        
        return Response({"message": f"{updated_count} resident(s) inactivated successfully."}, status=status.HTTP_200_OK)
    


class BulkDeleteResident(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, *args, **kwargs):
        """
        Bulk delete Resident records.
        Expects in request.data:
        - ids (list[int]): list of Resident IDs to delete
        """
        ids = request.data.get('ids')

        if not isinstance(ids, list) or not ids:
            return Response(
                {"error": "A non-empty list of 'ids' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            actor = request.user.member
        except Member.DoesNotExist:
            return Response(
                {"error": "Requesting user is not linked to a Member."},
                status=status.HTTP_400_BAD_REQUEST
            )

        resident_qs = get_list_or_404(Resident, id__in=ids)
        affected_members = set()

        with transaction.atomic():
            for resident in resident_qs:
                affected_members.add(resident.member.id)

                # Optional: Audit trail logging
                create_audit_trail(
                    member=actor,
                    event_type='RESIDENT_DELETED',
                    table_name='Resident',
                    row_id=resident.id,
                    old_data={"is_active": resident.is_active},
                    new_data={"deleted": True},
                    description=f"Resident id={resident.id} deleted."
                )

                # Delete the resident entry
                resident.delete()
                
            unit_id = request.data.get('unit_id')
            resident_exists = Resident.objects.filter(unit_id=unit_id).exists()
            # print(resident_exists)
            if not resident_exists:
                # owner = Owner.objects.filter(unit_id=unit_id).exists()
                # if not owner:
                unit = Unit.objects.get(id=unit_id.id if hasattr(unit_id, 'id') else unit_id)
                # print(unit)
                unit.unit_status = 'available'
                unit.status_color = unit.STATUS_COLORS['available']
                unit.save()
        # Optional: Log the deletion of affected members
            # Update or delete affected members
            # for member_id in affected_members:
            #     still_exists = (
            #         Resident.objects.filter(member_id=member_id, is_active=True).exists() or
            #         UnitStaff.objects.filter(member_id=member_id, is_active=True).exists() or
            #         Owner.objects.filter(member_id=member_id).exists()
            #     )
            #     if not still_exists:
            #         Member.objects.filter(id=member_id).delete()
            for member_id in affected_members:
                still_exists = (
                    Resident.objects.filter(member_id=member_id, is_active=True).exists() or
                    UnitStaff.objects.filter(member_id=member_id, is_active=True).exists() or
                    Owner.objects.filter(member_id=member_id).exists()
                )
                if not still_exists:
                    # Fixed: Correct field reference using double underscore
                    MembersRole.objects.filter(member__id=member_id).delete()

                    # Now delete the member
                    Member.objects.filter(id=member_id).delete()



        return Response(
            {"message": f"{len(resident_qs)} Residents deleted successfully."},
            status=status.HTTP_200_OK
        )






class CommMemberList(APIView):
    def get(self, request, format=None):
        # existing “array” filters
        types    = request.query_params.getlist('type')    # e.g. ['owner','resident']
        towers   = request.query_params.getlist('tower')
        units    = request.query_params.getlist('unit')
        statuses = request.query_params.getlist('status')
        # NEW: single‐value search box
        search = request.query_params.get('search', '').strip()

        def apply_common_filters(qs, member_field):
            # apply tower/unit/status just like before
            if towers:
                qs = qs.filter(unit__floor__tower__tower_name__in=towers)
            if units:
                qs = qs.filter(unit__unit_name__in=units)
            if statuses:
                bools = [(s.lower() == "active") for s in statuses]
                qs = qs.filter(**{f"{member_field}__is_comm_member__in": bools})

            # NEW: only search when user typed ≥ 3 chars
            if len(search) >= 3:
                qs = qs.filter(
                    Q(**{f"{member_field}__full_name__icontains": search}) |
                    Q(**{f"{member_field}__general_email__icontains": search})
                )
            return qs

        result = {}
        wants_owner    = not types or 'owner'    in types
        wants_resident = not types or 'resident' in types
        wants_staff    = not types or 'staff'    in types

        if wants_owner:
            owners_qs = apply_common_filters(Owner.objects.all(), 'member')
            result["owners"] = AddExistingOwners(owners_qs, many=True).data

        # if wants_resident:
        #     # residents_qs = apply_common_filters(Resident.objects.all(), 'member').order_by('id')
        #     residents_qs = apply_common_filters(Resident.objects.filter(is_active=True), 'member').order_by('id')
        #     # de-duplicate residents by their user ID
        #     seen, distinct = set(), []
        #     for r in residents_qs:
        #         if r.member.id not in seen:
        #             seen.add(r.member.id)
        #             distinct.append(r)
        #     result["resident_members"] = AddExistingResident(distinct, many=True).data
        if wants_resident:
            residents_qs = apply_common_filters(Resident.objects.filter(is_active=True), 'member').order_by('id')
            result["resident_members"] = AddExistingResident(residents_qs, many=True).data


        if wants_staff:
            staff_qs = apply_common_filters(UnitStaff.objects.filter(is_active=True), 'member')
            # staff_qs = apply_common_filters(UnitStaff.objects.all(), 'member')
            result["unit_staff"] = AddExistingUnitStaff(staff_qs, many=True).data

        return Response(result)