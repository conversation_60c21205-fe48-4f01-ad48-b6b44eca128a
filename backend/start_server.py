#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to start Django development server with proper configuration for React Native app.
This script ensures the server is accessible from Android emulator and other devices.
"""
import os
import sys
import subprocess
import socket

def check_port_available(host, port):
    """Check if a port is available on the given host."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex((host, port))
            return result != 0  # Port is available if connection fails
    except Exception:
        return True

def get_local_ip():
    """Get the local IP address of the machine."""
    try:
        # Connect to a remote address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "*************"  # Fallback to the IP in your config

def main():
    # Set Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
    
    # Get current directory (should be backend folder)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Check if we're in the backend directory
    if not os.path.exists(os.path.join(current_dir, 'manage.py')):
        print("Error: This script should be run from the backend directory.")
        print("Current directory:", current_dir)
        sys.exit(1)
    
    # Check if port 8000 is available
    if not check_port_available('0.0.0.0', 8000):
        print("Warning: Port 8000 is already in use.")
        print("Please stop any existing Django server or use a different port.")
        
        # Try to find the process using port 8000
        try:
            result = subprocess.run(['netstat', '-ano', '|', 'findstr', ':8000'], 
                                  shell=True, capture_output=True, text=True)
            if result.stdout:
                print("Processes using port 8000:")
                print(result.stdout)
        except Exception:
            pass
        
        response = input("Do you want to continue anyway? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    # Get local IP for information
    local_ip = get_local_ip()
    
    print("=" * 60)
    print("Starting Django Development Server")
    print("=" * 60)
    print(f"Server will be accessible at:")
    print(f"  • Local: http://127.0.0.1:8000")
    print(f"  • Network: http://{local_ip}:8000")
    print(f"  • Android Emulator: http://********:8000")
    print(f"  • iOS Simulator: http://localhost:8000")
    print("=" * 60)
    print("Press Ctrl+C to stop the server")
    print("=" * 60)
    
    try:
        # Start Django development server on all interfaces
        subprocess.run([
            sys.executable, 'manage.py', 'runserver', '0.0.0.0:8000'
        ], cwd=current_dir)
    except KeyboardInterrupt:
        print("\nServer stopped.")
    except Exception as e:
        print(f"Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
