#!/usr/bin/env python3
"""
Simple test script to verify Django server is working
"""
import requests
import json
import time

def test_server():
    base_url = "http://0.0.0.0:8000"
    
    print("=== Testing Django Server ===")
    print(f"Base URL: {base_url}")
    
    # Test 1: Health check endpoint
    try:
        print("\n1. Testing health check endpoint...")
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: Check status endpoint
    try:
        print("\n2. Testing check_status endpoint...")
        response = requests.post(
            f"{base_url}/user/check_status/",
            json={"authenticator": "<EMAIL>"},
            timeout=5
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Test from Android emulator perspective
    try:
        print("\n3. Testing from Android emulator perspective (10.0.2.2)...")
        response = requests.get("http://10.0.2.2:8000/", timeout=5)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_server() 